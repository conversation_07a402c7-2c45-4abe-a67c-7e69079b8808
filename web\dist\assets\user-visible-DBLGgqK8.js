import{d as C,i as a,ab as O,ac as d,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 4C9.567 4 8 5.567 8 7.5 8 9.433 9.567 11 11.5 11 13.433 11 15 9.433 15 7.5 15 5.567 13.433 4 11.5 4zM6 7.5C6 4.46243 8.46243 2 11.5 2 14.5376 2 17 4.46243 17 7.5 17 10.5376 14.5376 13 11.5 13 8.46243 13 6 10.5376 6 7.5zM8 16C5.79086 16 4 17.7909 4 20H11.05V22H2V20C2 16.6863 4.68629 14 8 14H11V16H8zM16.2476 19.0002V17.0002H18.7476V19.0002H16.2476z"}},{tag:"path",attrs:{fill:"currentColor",d:"M17.4998 22.5C21.9181 22.5 23.5898 18 23.5898 18C23.5898 18 21.9161 13.5 17.4998 13.5C13.0834 13.5 11.4098 18 11.4098 18C11.4098 18 13.0814 22.5 17.4998 22.5ZM17.498 20.5C14.8825 20.5 13.6285 18 13.6285 18C13.6285 18 14.8774 15.5 17.498 15.5C20.1187 15.5 21.3685 18 21.3685 18C21.3685 18 20.1136 20.5 17.498 20.5Z"}}]},m=C({name:"UserVisibleIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=O(t),p=a(()=>["t-icon","t-icon-user-visible",i.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>d(b,v.value)}});export{m as default};
