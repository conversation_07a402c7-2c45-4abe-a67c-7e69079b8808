import{d,i as a,ab as C,ac as O,ad as m}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M15.5005 10H18V13L23 10.523L23 21.477L18 19V22H1V10H2.55051C1.90223 9.36474 1.5 8.47935 1.5 7.5C1.5 5.567 3.067 4 5 4C6.01874 4 6.93582 4.43525 7.57544 5.12993C7.9872 2.78327 10.0353 1 12.5 1C15.2614 1 17.5 3.23858 17.5 6C17.5 7.6356 16.7147 9.08777 15.5005 10ZM8.28962 8.69794C8.10893 9.19401 7.81923 9.63769 7.44949 10H9.49951C9.02297 9.64197 8.61249 9.20076 8.28962 8.69794Z"}}]},g=d({name:"VideoCamera2FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:s}=C(t),p=a(()=>["t-icon","t-icon-video-camera-2-filled",i.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(y,v.value)}});export{g as default};
