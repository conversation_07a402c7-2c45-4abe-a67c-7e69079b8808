import{d as O,i as a,ab as d,ac as m,ad as y}from"./index-8d2rRG_R.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10.8786 22.6174L12.1576 22.4043C14.0863 22.0828 15.5 20.4141 15.5 18.4587V16.0001H20.8195C22.0554 16.0001 22.9955 14.8903 22.7923 13.6713L21.1257 3.67128C20.9649 2.7069 20.1306 2.00008 19.1529 2.00008H7V13.1979L10.8786 22.6174ZM12.1131 20.3626L9 12.8023V4.00008H19.1529L20.8195 14.0001H13.5V18.4587C13.5 19.3375 12.9289 20.1005 12.1131 20.3626ZM4 14.0001V2.00008H2V14.0001H4Z"}}]},h=O({name:"ThumbDownIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=d(r),p=a(()=>["t-icon","t-icon-thumb-down",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>m(b,v.value)}});export{h as default};
