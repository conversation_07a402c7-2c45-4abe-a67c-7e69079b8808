import{d,i as a,ab as O,ac as y,ad as m}from"./index-8d2rRG_R.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M19 22.7095L9.7369 17.4998H5L5 6.49976H9.7369L19 1.29004L19 22.7095ZM8.99882 8.49976H7L7 15.4998L8.99882 15.4998L8.99882 8.49976ZM10.9988 15.9149L17 19.29L17 4.70949L10.9988 8.08465L10.9988 15.9149Z"}}]},L=d({name:"SoundMuteIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=O(r),u=a(()=>["t-icon","t-icon-sound-mute",o.value]),p=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:u.value,style:p.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(b,v.value)}});export{L as default};
