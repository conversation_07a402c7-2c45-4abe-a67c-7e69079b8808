import{d as y,i as a,ab as d,ac as O,ad as m}from"./index-8d2rRG_R.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22 2H2V11H22V2zM7.00391 5.5L7.00391 7.50391H5V5.5H7.00391zM11.0039 5.5L11.0039 7.50391H9V5.5H11.0039zM22 13H2V22H22V13zM7.00391 16.5V18.5039H5V16.5H7.00391zM11.0039 18.5039H9V16.5H11.0039V18.5039z"}}]},H=y({name:"SystemApplicationFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:i,style:c}=d(r),p=a(()=>["t-icon","t-icon-system-application-filled",i.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>O(b,f.value)}});export{H as default};
