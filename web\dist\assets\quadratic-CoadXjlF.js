import{d as f,i as a,ab as d,ac as O,ad as y}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.2533 3H23V5H12.7467L7.88216 21.4178L3.66667 14.6429H1V12.6429H4.77778L7.22895 16.5822L11.2533 3ZM22 9.99999L22 12.1363C22 12.7327 21.7339 13.298 21.2742 13.6779L19.0696 15.5L21.2742 17.3221C21.7338 17.702 22 18.2673 22 18.8637L22 21L20 21L20 18.8637L17.5 16.7974L15 18.8637L15 21L13 21L13 18.8637C13 18.2673 13.2662 17.702 13.7258 17.3221L15.9304 15.5L13.7258 13.6779C13.2662 13.298 13 12.7327 13 12.1363L13 10L15 10L15 12.1363L17.5 14.2026L20 12.1363L20 10L22 9.99999Z"}}]},g=f({name:"QuadraticIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:s}=d(t),p=a(()=>["t-icon","t-icon-quadratic",i.value]),u=a(()=>c(c({},s.value),r.style)),L=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(m,L.value)}});export{g as default};
