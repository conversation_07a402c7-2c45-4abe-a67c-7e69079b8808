import{d,i as a,ab as O,ac as m,ad as y}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.0041 3C8.46675 3 5.35758 4.83591 3.57841 7.61157L3.03877 8.45347L1.35498 7.37418L1.89462 6.53229C4.02599 3.20715 7.75697 1 12.0041 1C16.2512 1 19.9822 3.20715 22.1136 6.53229L22.6532 7.37418L20.9694 8.45347L20.4298 7.61157C18.6506 4.83591 15.5414 3 12.0041 3ZM11.9927 6.98828C10.2246 6.98828 8.66999 7.90531 7.77958 9.29443L7.23994 10.1363L5.55615 9.05704L6.09579 8.21515C7.3384 6.27656 9.5148 4.98828 11.9927 4.98828C14.4707 4.98828 16.6471 6.27656 17.8897 8.21515L18.4293 9.05704L16.7455 10.1363L16.2059 9.29443C15.3155 7.90531 13.7609 6.98828 11.9927 6.98828ZM5.00019 11H19.0002V23H5.00019V11ZM11.0002 15V17.0039H13.0041V15H11.0002Z"}}]},g=d({name:"RemoteWaveFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-remote-wave-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:f})}}));return()=>m(C,v.value)}});export{g as default};
