import{d as O,i as a,ab as d,ac as L,ad as m}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){m(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 25 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M3 1.58594L22.4142 21.0002L21 22.4144L17 18.4144V20.0002H0V4.00015H2.58579L1.58579 3.00015L3 1.58594ZM4.58579 6.00015H2V18.0002H15V16.4144L4.58579 6.00015ZM7.58171 3.99869L17 4.00085L17 8.43397L24 4.23397V16.0002L24.0035 19.4162L22 17.4146V7.76634L17.0007 10.7659L17.0039 13.4209L15 11.415L15 6.00141L9.58704 6.00211L7.58171 3.99869Z"}}]},g=O({name:"VideoCameraOffIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=d(t),p=a(()=>["t-icon","t-icon-video-camera-off",o.value]),f=a(()=>c(c({},s.value),r.style)),u=a(()=>({class:p.value,style:f.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>L(y,u.value)}});export{g as default};
