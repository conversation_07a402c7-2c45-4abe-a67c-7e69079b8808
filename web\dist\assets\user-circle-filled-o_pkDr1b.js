import{d as v,i as a,ab as d,ac as O,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 12C23 15.3454 21.5066 18.3421 19.15 20.3595C17.2272 22.0057 14.7297 23 12 23C9.2703 23 6.77283 22.0057 4.85 20.3595C2.49344 18.3421 1 15.3454 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12ZM16 8.5C16 6.29086 14.2091 4.5 12 4.5C9.79086 4.5 8 6.29086 8 8.5C8 10.7091 9.79086 12.5 12 12.5C14.2091 12.5 16 10.7091 16 8.5ZM18.5 18.225V18C18.5 15.7909 16.7091 14 14.5 14H9.5C7.29086 14 5.5 15.7909 5.5 18V18.225C5.70642 18.4405 5.92348 18.6457 6.15034 18.8399C7.72404 20.1872 9.76583 21 12 21C14.2342 21 16.276 20.1872 17.8497 18.8399C18.0765 18.6457 18.2936 18.4405 18.5 18.225Z"}}]},g=v({name:"UserCircleFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=d(t),p=a(()=>["t-icon","t-icon-user-circle-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(m,C.value)}});export{g as default};
