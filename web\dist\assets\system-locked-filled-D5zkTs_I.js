import{d as v,i as a,ab as y,ac as O,ad as m}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 2H1V18H13V16C13 15.3015 13.1102 14.6287 13.3141 13.998H3V3.99805H21V9.67393C21.7204 9.8441 22.3945 10.1341 23 10.5218V2zM3 20H13V22H3V20z"}},{tag:"path",attrs:{fill:"currentColor",d:"M22.7515 14.75V15.5H24V22H15V15.5H16.2515V14.75C16.2515 12.9551 17.7065 11.5 19.5015 11.5C21.2964 11.5 22.7515 12.9551 22.7515 14.75ZM20.7515 14.75C20.7515 14.0596 20.1918 13.5 19.5015 13.5C18.8111 13.5 18.2515 14.0596 18.2515 14.75V15.5H20.7515V14.75Z"}}]},V=v({name:"SystemLockedFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=y(r),p=a(()=>["t-icon","t-icon-system-locked-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:d=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:d})}}));return()=>O(C,f.value)}});export{V as default};
