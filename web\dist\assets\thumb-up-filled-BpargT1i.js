import{d,i as a,ab as O,ac as m,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.1578 1.59573C14.0866 1.91719 15.5002 3.58595 15.5002 5.54131L15.5002 7.99992 20.8198 7.99992C22.0557 7.99992 22.9958 9.10966 22.7926 10.3287L21.1259 20.3287C20.9652 21.2931 20.1308 21.9999 19.1531 21.9999H7.00024L7.00025 10.8021 10.8789 1.38257 12.1578 1.59573zM4.00024 21.9999H2.00024L2.00025 9.99992H4.00025L4.00024 21.9999z"}}]},h=d({name:"ThumbUpFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-thumb-up-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>m(b,f.value)}});export{h as default};
