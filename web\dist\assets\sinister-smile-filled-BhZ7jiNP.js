import{d,i,ab as O,ac as m,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var C={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12 23C5.92487 23 1 18.0751 1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23ZM10 9H6V12H8V11H10V9ZM18 9H14V12H16V11H18V9ZM9.40076 14.4993L8.90004 13.6337L7.16882 14.6351L7.66955 15.5007C8.53256 16.9926 10.1481 18 12 18C13.852 18 15.4675 16.9926 16.3305 15.5007L16.8312 14.6351L15.1 13.6337L14.5993 14.4993C14.0791 15.3986 13.1092 16 12 16C10.8909 16 9.92099 15.3986 9.40076 14.4993Z"}}]},g=d({name:"SinisterSmileFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=i(()=>e.size),{className:a,style:c}=O(t),p=i(()=>["t-icon","t-icon-sinister-smile-filled",a.value]),u=i(()=>s(s({},c.value),r.style)),f=i(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>m(C,f.value)}});export{g as default};
