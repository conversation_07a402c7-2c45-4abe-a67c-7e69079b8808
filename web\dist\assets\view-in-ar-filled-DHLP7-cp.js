import{d,i as a,ab as O,ac as y,ad as V}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){V(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M6.5 1.5H1.5V6.5H3.5V3.5H6.5V1.5zM22.5 1.5H17.5V3.5H20.5V6.5H22.5V1.5zM18.0549 7.34129L11.9997 3.8453 5.94548 7.34069 11.9991 10.8345 18.0549 7.34129zM19.0614 9.06958L12.999 12.5666V19.5778L19.0619 16.0774 19.0614 9.06958zM10.999 19.5769V12.5665L4.93841 9.06867 4.9375 16.0774 10.999 19.5769zM3.5 20.5V17.5H1.5V22.5H6.5V20.5H3.5zM22.5 22.5V17.5H20.5V20.5H17.5V22.5H22.5z"}}]},g=d({name:"ViewInArFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=O(t),p=a(()=>["t-icon","t-icon-view-in-ar-filled",i.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
