import{d,i as a,ab as O,ac as y,ad as L}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){L(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22 2V22H2V2H22ZM6.65692 13.3719L18.3719 6.34292L17.3429 4.62793L9.45531 9.36049L7.38843 5.64012L5.64012 6.6114L7.73937 10.3901L5.62793 11.6569L6.65692 13.3719ZM16.2605 13.6098L18.3719 12.3429L17.3429 10.6279L5.62793 17.6569L6.65692 19.3719L14.5445 14.6393L16.6114 18.3597L18.3597 17.3884L16.2605 13.6098Z"}}]},g=d({name:"StreetRoadFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-street-road-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{g as default};
