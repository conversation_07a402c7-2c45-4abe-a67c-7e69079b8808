import{d as C,i as a,ab as d,ac as y,ad as O}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){O(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13.5 16C13.5 16.8284 12.8284 17.5 12 17.5C11.1716 17.5 10.5 16.8284 10.5 16C10.5 15.1716 11.1716 14.5 12 14.5C12.8284 14.5 13.5 15.1716 13.5 16Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M14.3474 0.65918L17.5262 5.11576L19.624 4.40072L21.5383 9.99976H22.5V21.9998H1.5V9.99976H2.00953L2.00962 9.99025L2.65847 9.99634L14.3474 0.65918ZM9.39652 9.99976L19.4247 9.99976L18.3876 6.96676L16.8657 7.45386L9.39652 9.99976ZM7.83949 8.41748L15.55 5.78935L13.9456 3.53995L7.83949 8.41748ZM5.5 12H3.5V14C4.60457 14 5.5 13.1046 5.5 12ZM15.5 16C15.5 14.067 13.933 12.5 12 12.5C10.067 12.5 8.5 14.067 8.5 16C8.5 17.933 10.067 19.5 12 19.5C13.933 19.5 15.5 17.933 15.5 16ZM20.5 20V18C19.3954 18 18.5 18.8954 18.5 20H20.5ZM18.5 12C18.5 13.1046 19.3954 14 20.5 14V12H18.5ZM3.5 20H5.5C5.5 18.8954 4.60457 18 3.5 18V20Z"}}]},L=C({name:"MoneyFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=d(r),p=a(()=>["t-icon","t-icon-money-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{L as default};
