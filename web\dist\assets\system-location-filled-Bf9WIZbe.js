import{d as C,i as a,ab as d,ac as y,ad as O}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){O(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M23 2H1V18H11.75V16.75C11.75 15.7699 11.9589 14.8385 12.3346 13.998H3V3.99805H21V10.4781C21.7402 10.7734 22.4159 11.196 23 11.7188V2zM3 20H11.75V22H3V20z"}},{tag:"path",attrs:{fill:"currentColor",d:"M18.5 23.7013L17.9453 23.332L17.9432 23.3306L17.9392 23.3279L17.9271 23.3198L17.8874 23.2925C17.8542 23.2694 17.808 23.2368 17.7505 23.1949C17.6356 23.1113 17.4752 22.9906 17.2845 22.836C16.9045 22.5281 16.3963 22.0799 15.8852 21.5179C14.8903 20.4239 13.75 18.7512 13.75 16.75C13.75 14.1266 15.8766 12 18.5 12C21.1234 12 23.25 14.1266 23.25 16.75C23.25 18.7512 22.1097 20.4239 21.1148 21.5179C20.6037 22.0799 20.0955 22.5281 19.7155 22.836C19.5248 22.9906 19.3644 23.1113 19.2495 23.1949C19.192 23.2368 19.1458 23.2694 19.1126 23.2925L19.0729 23.3198L19.0608 23.3279L19.0568 23.3306L19.0553 23.3316L18.5 23.7013ZM19.75 16H17.25V18H19.75V16Z"}}]},b=C({name:"SystemLocationFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=d(r),p=a(()=>["t-icon","t-icon-system-location-filled",o.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:v})}}));return()=>y(m,f.value)}});export{b as default};
