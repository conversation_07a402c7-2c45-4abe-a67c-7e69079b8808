import{d,i as a,ab as O,ac as y,ad as g}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){g(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var h={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M22 2H2V5C2 7.20914 3.79086 9 6 9 7.19469 9 8.26706 8.47624 9 7.64582 9.73295 8.47624 10.8053 9 12 9 13.1947 9 14.2671 8.47624 15 7.64582 15.7329 8.47624 16.8053 9 18 9 20.2091 9 22 7.20914 22 5V2zM2 22.0004V9.47266C3.06151 10.4227 4.46329 11.0004 6 11.0004 7.09262 11.0004 8.11773 10.7075 9 10.1973 9.88227 10.7075 10.9074 11.0004 12 11.0004 13.0926 11.0004 14.1177 10.7075 15 10.1973 15.8823 10.7075 16.9074 11.0004 18 11.0004 19.5367 11.0004 20.9385 10.4227 22 9.47266V22.0004H16.5V14.0005H7.5V22.0004H2z"}},{tag:"path",attrs:{fill:"currentColor",d:"M9.5 16.0005V22.0005H14.5V16.0005H9.5Z"}}]},b=d({name:"Shop1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-shop-1-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(h,f.value)}});export{b as default};
