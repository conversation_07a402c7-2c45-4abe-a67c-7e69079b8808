import{d as O,i as a,ab as y,ac as L,ad as d}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M13 2V8H11V2H13ZM4 2.58579L9.91421 8.5L8.5 9.91421L2.58579 4L4 2.58579ZM21.4142 4L15.5 9.91421L14.0858 8.5L20 2.58579L21.4142 4ZM10.2676 11C10.6134 10.4022 11.2597 10 12 10C13.1046 10 14 10.8954 14 12C14 13.1046 13.1046 14 12 14C11.2597 14 10.6134 13.5978 10.2676 13H2V11H10.2676ZM16 11H22V13H16V11ZM9.91421 15.5L4 21.4142L2.58579 20L8.5 14.0858L9.91421 15.5ZM15.5 14.0858L21.4142 20L20 21.4142L14.0858 15.5L15.5 14.0858ZM13 16V22H11V16H13Z"}}]},g=O({name:"PreciseMonitorIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=y(t),p=a(()=>["t-icon","t-icon-precise-monitor",o.value]),u=a(()=>s(s({},c.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>L(m,v.value)}});export{g as default};
