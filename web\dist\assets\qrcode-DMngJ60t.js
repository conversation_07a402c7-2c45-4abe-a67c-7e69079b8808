import{d as v,i as a,ab as f,ac as d,ad as O}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2H11V11H2V2ZM4 4V9H9V4H4ZM13 2H22V11H13V2ZM15 4V9H20V4H15ZM5.5 5.5H7.50391V7.50391H5.5V5.5ZM16.5 5.5H18.5039V7.50391H16.5V5.5ZM12.9961 12.9961H15V15H12.9961V12.9961ZM19.9961 12.9961H22V15H19.9961V12.9961ZM2 13H11V22H2V13ZM4 15V20H9V15H4ZM15.9961 15.9961H18V17.9961H20V19.9961H22V22H19.9961V20H17.9961V18H15.9961V15.9961ZM5.5 16.5H7.50391V18.5039H5.5V16.5ZM12.9961 19.9961H15V22H12.9961V19.9961Z"}}]},M=v({name:"QrcodeIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=f(t),H=a(()=>["t-icon","t-icon-qrcode",o.value]),V=a(()=>c(c({},s.value),r.style)),p=a(()=>({class:H.value,style:V.value,onClick:u=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:u})}}));return()=>d(y,p.value)}});export{M as default};
