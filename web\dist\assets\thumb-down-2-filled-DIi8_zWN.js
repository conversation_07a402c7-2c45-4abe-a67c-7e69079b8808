import{d,i as a,ab as O,ac as m,ad as y}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.7304 21.6641L13.4456 22.2358 17.15 13.9V2H6.51631C5.0356 2 3.77635 3.08034 3.5512 4.54383L2.36658 12.2438C2.087 14.0611 3.49304 15.7 5.3317 15.7H9.20047V18.154C9.20047 19.7465 10.2196 21.1605 11.7304 21.6641zM22.0004 13.9V2H19.1504L19.1508 13.9H22.0004z"}}]},h=d({name:"ThumbDown2FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),p=a(()=>["t-icon","t-icon-thumb-down-2-filled",l.value]),u=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>m(b,f.value)}});export{h as default};
