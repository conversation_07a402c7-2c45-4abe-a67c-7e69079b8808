import{d as v,i as a,ab as d,ac as O,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function L(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"g",attrs:{clipPath:"url(#clip0_8726_7351)"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M12.0004 0.186157L15.6172 3.26835L20.354 3.64636L20.7321 8.38325L23.8142 12L20.7321 15.6168L20.354 20.3537L15.6172 20.7317L12.0004 23.8139L8.38361 20.7317L3.64672 20.3537L3.26872 15.6168L0.186523 12L3.26872 8.38324L3.64672 3.64636L8.38361 3.26835L12.0004 0.186157ZM12.0004 2.81388L9.18808 5.21051L5.50481 5.50444L5.21088 9.18771L2.81425 12L5.21088 14.8123L5.50481 18.4956L9.18808 18.7895L12.0004 21.1862L14.8127 18.7895L18.496 18.4956L18.7899 14.8123L21.1865 12L18.7899 9.18771L18.496 5.50444L14.8127 5.21051L12.0004 2.81388ZM17.9146 9.50002L11.0004 16.4142L6.58617 12L8.00039 10.5858L11.0004 13.5858L16.5004 8.08581L17.9146 9.50002Z"}}]}]},b=v({name:"VerifiedIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=d(t),s=a(()=>["t-icon","t-icon-verified",i.value]),p=a(()=>L(L({},c.value),r.style)),u=a(()=>({class:s.value,style:p.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>O(g,u.value)}});export{b as default};
