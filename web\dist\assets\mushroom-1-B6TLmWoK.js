import{d as f,i as a,ab as m,ac as O,ad as d}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(C){return Object.getOwnPropertyDescriptor(e,C).enumerable})),r.push.apply(r,t)}return r}function i(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"g",attrs:{clipPath:"url(#clip0_8726_9460)"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M18.5433 5.45515C14.4905 1.40126 10.7205 1.96447 9.52781 3.15715C9.25234 3.43262 9.13286 3.80445 9.36211 4.51646C9.60625 5.27472 10.2035 6.22959 11.1232 7.35526C11.8602 8.25737 12.7651 9.22272 13.7705 10.2282C14.7759 11.2336 15.7413 12.1385 16.6434 12.8755C17.7691 13.7951 18.7239 14.3924 19.4822 14.6366C20.1942 14.8658 20.566 14.7463 20.8415 14.4709C22.034 13.2783 22.5965 9.50942 18.5433 5.45515ZM8.1136 1.74293C10.4356 -0.579103 15.525 -0.392804 19.9577 4.04113C24.3901 8.47467 24.5779 13.5629 22.2557 15.8851C21.2823 16.8585 20.0377 16.9165 18.8693 16.5403C18.0004 16.2606 17.0912 15.7232 16.1776 15.0476C16.0284 15.2665 15.8732 15.5144 15.7105 15.7903C15.4022 16.3134 15.0922 16.8929 14.7609 17.5125C14.6925 17.6405 14.6231 17.7702 14.5526 17.9015C13.7565 19.3848 12.8181 21.0831 11.6481 22.25C10.6897 23.2059 9.46243 24.0534 7.86055 23.9972C6.29186 23.9422 4.64678 23.028 2.80965 21.1879C0.97313 19.3484 0.0590761 17.7033 0.00277407 16.136C-0.0547721 14.534 0.791503 13.3077 1.74955 12.3496C2.91735 11.1818 4.61507 10.2436 6.09794 9.44723C6.22746 9.37767 6.35546 9.30917 6.4818 9.24155C7.10301 8.90909 7.68408 8.59811 8.20843 8.28873C8.48432 8.12596 8.73226 7.97055 8.95117 7.82128C8.27556 6.90763 7.73811 5.99831 7.45835 5.12941C7.08214 3.96094 7.14021 2.71632 8.1136 1.74293ZM10.2164 9.37767C9.90322 9.59868 9.56695 9.80935 9.22475 10.0113C8.66102 10.3439 8.03539 10.6786 7.4141 11.011C7.29046 11.0772 7.16699 11.1433 7.04421 11.2092C5.5149 12.0305 4.08819 12.8394 3.16377 13.7638C2.36445 14.5631 1.97326 15.2785 2.00148 16.0642C2.03095 16.8845 2.52591 18.073 4.22499 19.7748C5.92345 21.476 7.11086 21.9697 7.93067 21.9984C8.71728 22.026 9.4345 21.6331 10.2358 20.834C11.1608 19.9114 11.9694 18.4854 12.7904 16.9557C12.8571 16.8314 12.924 16.7064 12.9909 16.5811C13.3223 15.9613 13.6561 15.3371 13.9876 14.7747C14.1895 14.4323 14.4001 14.0958 14.6211 13.7824C13.8809 13.1331 13.1215 12.4076 12.3563 11.6424C11.5911 10.8772 10.8657 10.1178 10.2164 9.37767Z"}}]}]},h=f({name:"Mushroom1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:C,style:s}=m(t),c=a(()=>["t-icon","t-icon-mushroom-1",C.value]),p=a(()=>i(i({},s.value),r.style)),u=a(()=>({class:c.value,style:p.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(y,u.value)}});export{h as default};
