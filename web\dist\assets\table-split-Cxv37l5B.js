import{d as V,i as a,ab as f,ac as O,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){y(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2H22V22H2V2ZM4 4V6H11V4H4ZM13 4V6H20V4H13ZM20 8H4V20H20V8ZM10.998 9.99805H13.002V12.002H10.998V9.99805ZM4.99805 12.998H7.00195V15.002H4.99805V12.998ZM7.99805 12.998H10.002V15.002H7.99805V12.998ZM10.998 12.998H13.002V15.002H10.998V12.998ZM13.998 12.998H16.002V15.002H13.998V12.998ZM16.998 12.998H19.002V15.002H16.998V12.998ZM10.998 15.998H13.002V18.002H10.998V15.998Z"}}]},m=V({name:"TableSplitIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=f(r),p=a(()=>["t-icon","t-icon-table-split",l.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:H=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:H})}}));return()=>O(b,v.value)}});export{m as default};
