import{d,i as a,ab as O,ac as y,ad as m}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M5 6.4998V17.4998L1 17.5 1 6.5 5 6.4998zM7 18.2099L15.0004 22.7095 15.0004 1.29004 7 5.7896V18.2099zM20.0004 10.5857L17.8791 8.46436 16.4648 9.87857 18.5862 11.9999 16.4648 14.1212 17.8791 15.5354 20.0004 13.4141 22.1217 15.5354 23.5359 14.1212 21.4146 11.9999 23.5359 9.87857 22.1217 8.46436 20.0004 10.5857z"}}]},P=d({name:"SoundMute1FilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:l,style:c}=O(r),u=a(()=>["t-icon","t-icon-sound-mute-1-filled",l.value]),p=a(()=>s(s({},c.value),t.style)),f=a(()=>({class:u.value,style:p.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(b,f.value)}});export{P as default};
