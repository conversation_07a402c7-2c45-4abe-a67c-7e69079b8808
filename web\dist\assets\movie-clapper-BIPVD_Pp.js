import{d as L,i as a,ab as O,ac as y,ad as d}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function p(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){d(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2L22 2L22 22L2 22L2 2ZM4 4L4 8L8.86496 8L5.53163 4L4 4ZM8.13504 4L11.4684 8L15.865 8L12.5316 4L8.13504 4ZM15.135 4L18.4684 8H20V4L15.135 4ZM20 10L4 10L4 20L20 20L20 10ZM15 14H9V12H15V14Z"}}]},g=L({name:"MovieClapperIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=O(t),s=a(()=>["t-icon","t-icon-movie-clapper",o.value]),u=a(()=>p(p({},c.value),r.style)),v=a(()=>({class:s.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(m,v.value)}});export{g as default};
