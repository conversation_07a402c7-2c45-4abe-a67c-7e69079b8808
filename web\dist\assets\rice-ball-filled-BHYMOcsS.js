import{d as v,i as a,ab as d,ac as O,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var b={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M9.01735 2.40327C9.84697 1.51548 10.8866 1.00001 12 1C13.1134 1.00001 14.153 1.51549 14.9826 2.40327C16.5862 4.1193 18.3279 6.57308 19.7793 9.18426C21.2271 11.7888 22.4277 14.6246 22.8919 17.1081C23.0423 17.9132 23.0609 18.736 22.7839 19.5399C22.5034 20.3537 21.9563 21.0435 21.1581 21.6429C20.7914 21.9183 20.2371 22.1003 19.7151 22.2351C19.1449 22.3824 18.4395 22.5143 17.6481 22.6253C17.4389 22.6546 17.2226 22.6827 17 22.7092V14H7V22.7092C6.77739 22.6827 6.56111 22.6546 6.35192 22.6253C5.56051 22.5143 4.85507 22.3824 4.28486 22.2351C3.76283 22.1003 3.20861 21.9183 2.84184 21.6429C2.04366 21.0435 1.49654 20.3537 1.21607 19.5399C0.939032 18.736 0.957642 17.9132 1.10811 17.1081C1.57226 14.6246 2.77291 11.7888 4.22063 9.18426C5.67204 6.57308 7.41375 4.1193 9.01735 2.40327ZM10.9983 5.12793V7.13189H13.0018V5.12793H10.9983ZM8.2959 9.18673V11.1907H10.2994V9.18673H8.2959ZM15.7043 9.18673H13.7007V11.1907H15.7043V9.18673Z"}},{tag:"path",attrs:{fill:"currentColor",d:"M15 16V22.8945C14.0471 22.9595 13.0342 22.9974 12.0025 23H11.9975C10.9658 22.9974 9.95292 22.9595 9 22.8945V16H15Z"}}]},m=v({name:"RiceBallFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:s}=d(t),p=a(()=>["t-icon","t-icon-rice-ball-filled",l.value]),u=a(()=>c(c({},s.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(b,C.value)}});export{m as default};
