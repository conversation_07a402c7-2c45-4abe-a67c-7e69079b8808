import{d as v,i as a,ab as d,ac as O,ad as y}from"./index-8d2rRG_R.js";function o(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?o(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M21.5 4.5C21.5 6.433 19.933 8 18 8C17.0667 8 16.2187 7.63468 15.5912 7.03924L8.40532 11.188C8.46723 11.4486 8.5 11.7205 8.5 12C8.5 12.2795 8.46723 12.5514 8.40532 12.812L15.5912 16.9608C16.2187 16.3653 17.0667 16 18 16C19.933 16 21.5 17.567 21.5 19.5C21.5 21.433 19.933 23 18 23C16.067 23 14.5 21.433 14.5 19.5C14.5 19.2226 14.5323 18.9528 14.5933 18.694L7.40431 14.5435C6.77728 15.1364 5.9311 15.5 5 15.5C3.067 15.5 1.5 13.933 1.5 12C1.5 10.067 3.067 8.5 5 8.5C5.93111 8.5 6.77729 8.86359 7.40433 9.45653L14.5933 5.306C14.5323 5.04723 14.5 4.77739 14.5 4.5C14.5 2.567 16.067 1 18 1C19.933 1 21.5 2.567 21.5 4.5Z"}}]},g=v({name:"ShareFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=d(t),p=a(()=>["t-icon","t-icon-share-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>O(m,C.value)}});export{g as default};
