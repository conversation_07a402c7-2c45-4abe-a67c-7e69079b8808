import{d,i as a,ab as f,ac as O,ad as y}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){y(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var g={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M9 5C7.34315 5 6 6.34315 6 8C6 9.65685 7.34315 11 9 11V13C6.23858 13 4 15.2386 4 18V22H2V18C2 15.3075 3.52021 12.97 5.74913 11.7991C4.67847 10.882 4 9.52024 4 8C4 5.23858 6.23858 3 9 3H10V5H9ZM15 5C13.3431 5 12 6.34315 12 8C12 9.65685 13.3431 11 15 11C16.6569 11 18 9.65685 18 8C18 6.34315 16.6569 5 15 5ZM10 8C10 5.23858 12.2386 3 15 3C17.7614 3 20 5.23858 20 8C20 10.7614 17.7614 13 15 13C12.2386 13 10 10.7614 10 8ZM7 19C7 16.2386 9.23858 14 12 14H15V16H12C10.3431 16 9 17.3431 9 19V20H15V22H7V19ZM21 14V17H24V19H21V22H19V19H16V17H19V14H21Z"}}]},m=d({name:"UsergroupAddIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:c}=f(t),p=a(()=>["t-icon","t-icon-usergroup-add",o.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:v})}}));return()=>O(g,C.value)}});export{m as default};
