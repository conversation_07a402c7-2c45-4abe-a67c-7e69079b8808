import{d as O,i as a,ab as y,ac as d,ad as V}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),r.push.apply(r,t)}return r}function c(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){V(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M1 3L23 3V21H1L1 3ZM3 5L3 19H21V5L3 5ZM15 8V10.0476L16 9.33979V8H18V9.59847C18 10.0852 17.7638 10.5416 17.3666 10.8228L15.7034 12L17.3666 13.1772C17.7639 13.4584 18 13.9148 18 14.4015V16H16V14.6602L15 13.9524V16H13V8H15ZM7 8V11.4286H9V8H11V16H9V13.4286H7C5.89543 13.4286 5 12.5331 5 11.4286V8H7Z"}}]},g=O({name:"Screen4KIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:o,style:s}=y(t),p=a(()=>["t-icon","t-icon-screen-4k",o.value]),u=a(()=>c(c({},s.value),r.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>d(m,v.value)}});export{g as default};
