import{d,i as a,ab as O,ac as y,ad as g}from"./index-8d2rRG_R.js";function i(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(l){return Object.getOwnPropertyDescriptor(e,l).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?i(Object(r),!0).forEach(function(t){g(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M11.5 2C8.46243 2 6 4.46243 6 7.5 6 10.5376 8.46243 13 11.5 13 14.5376 13 17 10.5376 17 7.5 17 4.46243 14.5376 2 11.5 2zM18.8724 14.494L16.3928 17.0021 23.5021 17.0021V19.0021L16.3928 19.0021 18.8724 21.5102 17.4501 22.9163 12.5918 18.0021 17.4501 13.0879 18.8724 14.494z"}},{tag:"path",attrs:{fill:"currentColor",d:"M12.8762 14C12.0139 15.103 11.5 16.4915 11.5 18C11.5 19.5085 12.0139 20.897 12.8762 22H2V20C2 16.6863 4.68629 14 8 14H12.8762Z"}}]},w=d({name:"UserArrowLeftFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:l,style:c}=O(t),p=a(()=>["t-icon","t-icon-user-arrow-left-filled",l.value]),u=a(()=>s(s({},c.value),r.style)),f=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>y(m,f.value)}});export{w as default};
