import{d as f,i as a,ab as y,ac as O,ad as b}from"./index-8d2rRG_R.js";function l(e,n){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);n&&(t=t.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),r.push.apply(r,t)}return r}function s(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{};n%2?l(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M7 2V5H16.1707C16.472 4.14759 17.1476 3.47199 18 3.17071V2H20V3.17071C21.1652 3.58254 22 4.69378 22 6C22 7.30622 21.1652 8.41746 20 8.82929V19H8.82929C8.52801 19.8524 7.85241 20.528 7 20.8293V22H5V20.8293C4.14759 20.528 3.47199 19.8524 3.17071 19H2V17H3.17071C3.47199 16.1476 4.14759 15.472 5 15.1707L5 7H2V5H5V2H7ZM7 7V15.1707C7.85241 15.472 8.52801 16.1476 8.82929 17H18V8.82929C17.1476 8.52801 16.472 7.85241 16.1707 7H7ZM19 5C18.4477 5 18 5.44772 18 6C18 6.55228 18.4477 7 19 7C19.5523 7 20 6.55228 20 6C20 5.44772 19.5523 5 19 5ZM6 17C5.44772 17 5 17.4477 5 18C5 18.5523 5.44772 19 6 19C6.55228 19 7 18.5523 7 18C7 17.4477 6.55228 17 6 17Z"}}]},g=f({name:"SubwayLineIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:r}=n,t=a(()=>e.size),{className:i,style:c}=y(t),p=a(()=>["t-icon","t-icon-subway-line",i.value]),u=a(()=>s(s({},c.value),r.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var o;return(o=e.onClick)===null||o===void 0?void 0:o.call(e,{e:v})}}));return()=>O(d,C.value)}});export{g as default};
