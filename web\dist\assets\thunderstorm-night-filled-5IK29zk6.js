import{d as v,i as a,ab as d,ac as L,ad as O}from"./index-8d2rRG_R.js";function o(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?o(Object(t),!0).forEach(function(r){O(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):o(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var m={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M16.1313 0.901367L16.029 2.08597C16.0178 2.21553 16.0121 2.34687 16.0121 2.47979C16.0121 4.96789 18.0291 6.98489 20.5172 6.98489C20.6501 6.98489 20.7814 6.97917 20.911 6.96798L22.0956 6.8657L21.9933 8.0503C21.8458 9.75856 21.0391 11.2761 19.8311 12.3493C20.5702 13.2552 21.0167 14.4038 21.0167 15.6588C21.0167 17.871 19.634 19.7444 17.7014 20.5572L16.7796 20.9449L16.0042 19.1014L16.926 18.7137C18.1755 18.1881 19.0167 17.0041 19.0167 15.6588C19.0167 14.6066 18.5051 13.6582 17.6842 13.0396C17.5545 12.9419 17.4174 12.8527 17.2737 12.7729C16.7584 12.4868 16.1579 12.3214 15.5125 12.3214C15.5007 12.3214 15.4889 12.3215 15.4771 12.3216C15.4692 12.3217 15.4614 12.3218 15.4535 12.3219L14.5993 12.3352L14.4527 11.4935C14.1701 9.87034 12.8999 8.53412 11.2149 8.11784C10.8622 8.03072 10.4913 7.98406 10.1075 7.98406C10.0861 7.98406 10.0647 7.98421 10.0434 7.9845C7.60508 8.01758 5.70251 9.9216 5.70251 12.1889C5.70251 12.4514 5.72758 12.7074 5.77532 12.9552L5.95942 13.9106L5.00935 14.1208C3.83266 14.381 3 15.3817 3 16.5262C3 17.4741 3.56688 18.3187 4.43353 18.7334L5.33556 19.1651L4.47222 20.9692L3.57019 20.5375C2.06388 19.8166 1 18.3032 1 16.5262C1 14.6949 2.1256 13.1481 3.70819 12.4522C3.70441 12.3648 3.70251 12.277 3.70251 12.1889C3.70251 9.04272 6.1007 6.49027 9.16605 6.05095C9.77898 3.32609 12.1 1.24945 14.9467 1.00365L16.1313 0.901367ZM12.8767 13.656L10.8177 16.9794H14.8352L10.8125 23.3587L9.12075 22.2919L11.2096 18.9794H7.2259L11.1766 12.6027L12.8767 13.656Z"}}]},g=v({name:"ThunderstormNightFilledIcon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:i,style:c}=d(r),C=a(()=>["t-icon","t-icon-thunderstorm-night-filled",i.value]),p=a(()=>s(s({},c.value),t.style)),u=a(()=>({class:C.value,style:p.value,onClick:f=>{var l;return(l=e.onClick)===null||l===void 0?void 0:l.call(e,{e:f})}}));return()=>L(m,u.value)}});export{g as default};
