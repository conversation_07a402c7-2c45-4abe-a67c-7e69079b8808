import e from"./html-B8pBdPMQ.js";import n from"./haml-AZXGPtNI.js";import t from"./xml-sdJ4AIDG.js";import a from"./sql-BLtJtn59.js";import r from"./graphql-Caa12_AY.js";import i from"./css-DPfMkruS.js";import u from"./cpp-CofmeUqb.js";import c from"./c-BIGW1oBm.js";import s from"./javascript-BMMyXqK5.js";import o from"./shellscript-Yzrsuije.js";import d from"./lua-BbnMAYS6.js";import b from"./yaml-Buea-lGh.js";import"./java-CylS5w8V.js";import"./typescript-DlfHMoPT.js";import"./jsx-g9-lgVsj.js";import"./tsx-COt5Ahok.js";import"./regexp-CDVJQ6XC.js";import"./glsl-DplSGwfg.js";const m=Object.freeze(JSON.parse('{"displayName":"Ruby","name":"ruby","patterns":[{"captures":{"1":{"name":"keyword.control.class.ruby"},"2":{"name":"entity.name.type.class.ruby"},"5":{"name":"punctuation.separator.namespace.ruby"},"7":{"name":"punctuation.separator.inheritance.ruby"},"8":{"name":"entity.other.inherited-class.ruby"},"11":{"name":"punctuation.separator.namespace.ruby"}},"match":"\\\\b(class)\\\\s+(([0-9A-Z_a-z]+)((::)[0-9A-Z_a-z]+)*)\\\\s*((<)\\\\s*(([0-9A-Z_a-z]+)((::)[0-9A-Z_a-z]+)*))?","name":"meta.class.ruby"},{"captures":{"1":{"name":"keyword.control.module.ruby"},"2":{"name":"entity.name.type.module.ruby"},"5":{"name":"punctuation.separator.namespace.ruby"}},"match":"\\\\b(module)\\\\s+(([0-9A-Z_a-z]+)((::)[0-9A-Z_a-z]+)*)","name":"meta.module.ruby"},{"captures":{"1":{"name":"keyword.control.class.ruby"},"2":{"name":"punctuation.separator.inheritance.ruby"}},"match":"\\\\b(class)\\\\s*(<<)\\\\s*","name":"meta.class.ruby"},{"match":"(?<!\\\\.)\\\\belse(\\\\s)+if\\\\b","name":"invalid.deprecated.ruby"},{"captures":{"1":{"name":"variable.ruby"},"2":{"name":"keyword.operator.assignment.augmented.ruby"}},"match":"^\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*((&&|\\\\|\\\\|)=)"},{"captures":{"1":{"name":"keyword.control.ruby"},"3":{"name":"variable.ruby"},"4":{"name":"keyword.operator.assignment.augmented.ruby"}},"match":"(?<!\\\\.)\\\\b(case|if|elsif|unless|until|while)\\\\b\\\\s*(\\\\()*?\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*((&&|\\\\|\\\\|)=)"},{"captures":{"1":{"name":"variable.ruby"},"2":{"name":"keyword.operator.assignment.augmented.ruby"}},"match":"^\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*(([-%*+/]|\\\\*\\\\*|[\\\\&^|]|<<|>>)=)"},{"captures":{"1":{"name":"keyword.control.ruby"},"3":{"name":"variable.ruby"},"4":{"name":"keyword.operator.assignment.augmented.ruby"}},"match":"(?<!\\\\.)\\\\b(case|if|elsif|unless|until|while)\\\\b\\\\s*(\\\\()*?\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*(([-%*+/]|\\\\*\\\\*|[\\\\&^|]|<<|>>)=)"},{"captures":{"1":{"name":"variable.ruby"}},"match":"^\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*(?==[^=>])"},{"captures":{"1":{"name":"keyword.control.ruby"},"3":{"name":"variable.ruby"}},"match":"(?<!\\\\.)\\\\b(case|if|elsif|unless|until|while)\\\\b\\\\s*(\\\\()*?\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*=[^=>]"},{"captures":{"1":{"name":"punctuation.definition.constant.hashkey.ruby"}},"match":"(?>[A-Z_a-z]\\\\w*[!?]?)(:)(?!:)","name":"constant.language.symbol.hashkey.ruby"},{"captures":{"1":{"name":"punctuation.definition.constant.ruby"}},"match":"(?<!:)(:)(?>[A-Z_a-z]\\\\w*[!?]?)(?=\\\\s*=>)","name":"constant.language.symbol.hashkey.ruby"},{"match":"(?<!\\\\.)\\\\b(BEGIN|begin|case|class|else|elsif|END|end|ensure|for|if|in|module|rescue|then|unless|until|when|while)\\\\b(?![!?])","name":"keyword.control.ruby"},{"match":"(?<!\\\\.)\\\\bdo\\\\b","name":"keyword.control.start-block.ruby"},{"match":"(?<=\\\\{)(\\\\s+)","name":"meta.syntax.ruby.start-block"},{"match":"(?<!\\\\.)\\\\b(alias|alias_method|break|next|redo|retry|return|super|undef|yield)\\\\b(?![!?])|\\\\bdefined\\\\?|\\\\b(block_given|iterator)\\\\?","name":"keyword.control.pseudo-method.ruby"},{"match":"\\\\bnil\\\\b(?![!?])","name":"constant.language.nil.ruby"},{"match":"\\\\b(true|false)\\\\b(?![!?])","name":"constant.language.boolean.ruby"},{"match":"\\\\b(__(FILE|LINE)__)\\\\b(?![!?])","name":"variable.language.ruby"},{"match":"\\\\bself\\\\b(?![!?])","name":"variable.language.self.ruby"},{"match":"\\\\b(initialize|new|loop|include|extend|prepend|raise|fail|attr_reader|attr_writer|attr_accessor|attr|catch|throw|private|private_class_method|module_function|public|public_class_method|protected|refine|using)\\\\b(?![!?])","name":"keyword.other.special-method.ruby"},{"begin":"\\\\b(?<!\\\\.|::)(require(?:|_relative))\\\\b(?![!?])","captures":{"1":{"name":"keyword.other.special-method.ruby"}},"end":"$|(?=[#}])","name":"meta.require.ruby","patterns":[{"include":"$self"}]},{"captures":{"1":{"name":"punctuation.definition.variable.ruby"}},"match":"(@)[A-Z_a-z]\\\\w*","name":"variable.other.readwrite.instance.ruby"},{"captures":{"1":{"name":"punctuation.definition.variable.ruby"}},"match":"(@@)[A-Z_a-z]\\\\w*","name":"variable.other.readwrite.class.ruby"},{"captures":{"1":{"name":"punctuation.definition.variable.ruby"}},"match":"(\\\\$)[A-Z_a-z]\\\\w*","name":"variable.other.readwrite.global.ruby"},{"captures":{"1":{"name":"punctuation.definition.variable.ruby"}},"match":"(\\\\$)([!\\\\&\'+@`]|\\\\d+|[\\"$*,./:-?\\\\\\\\_~]|-[0FIadilpv])","name":"variable.other.readwrite.global.pre-defined.ruby"},{"begin":"\\\\b(ENV)\\\\[","beginCaptures":{"1":{"name":"variable.other.constant.ruby"}},"end":"]","name":"meta.environment-variable.ruby","patterns":[{"include":"$self"}]},{"match":"\\\\b[A-Z]\\\\w*(?=((\\\\.|::)[A-Za-z]|\\\\[))","name":"support.class.ruby"},{"match":"\\\\b((abort|at_exit|autoload|binding|callcc|caller|caller_locations|chomp|chop|eval|exec|exit|fork|format|gets|global_variables|gsub|lambda|load|local_variables|open|p|printf??|proc|putc|puts|rand|readlines??|select|set_trace_func|sleep|spawn|sprintf|srand|sub|syscall|system|test|trace_var|trap|untrace_var|warn)\\\\b(?![!?])|autoload\\\\?|exit!)","name":"support.function.kernel.ruby"},{"match":"\\\\b[A-Z_]\\\\w*\\\\b","name":"variable.other.constant.ruby"},{"begin":"(->)\\\\(","beginCaptures":{"1":{"name":"support.function.kernel.ruby"}},"end":"\\\\)","patterns":[{"begin":"(?=[\\\\&*A-Z_a-z])","end":"(?=[),])","patterns":[{"include":"#method_parameters"}]},{"include":"#method_parameters"}]},{"begin":"(?=def\\\\b)(?<=^|\\\\s)(def)\\\\s+((?>[A-Z_a-z]\\\\w*(?>\\\\.|::))?(?>[A-Z_a-z]\\\\w*(?>[!?]|=(?!>))?|===?|!=|>[=>]?|<=>|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?))\\\\s*(\\\\()","beginCaptures":{"1":{"name":"keyword.control.def.ruby"},"2":{"name":"entity.name.function.ruby"},"3":{"name":"punctuation.definition.parameters.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.definition.parameters.ruby"}},"name":"meta.function.method.with-arguments.ruby","patterns":[{"begin":"(?=[\\\\&*A-Z_a-z])","end":"(?=[),])","patterns":[{"include":"#method_parameters"}]},{"include":"#method_parameters"}]},{"begin":"(?=def\\\\b)(?<=^|\\\\s)(def)\\\\s+((?>[A-Z_a-z]\\\\w*(?>\\\\.|::))?(?>[A-Z_a-z]\\\\w*(?>[!?]|=(?!>))?|===?|!=|>[=>]?|<=>|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?))[\\\\t ](?=[\\\\t ]*[^#;\\\\s])","beginCaptures":{"1":{"name":"keyword.control.def.ruby"},"2":{"name":"entity.name.function.ruby"}},"end":"(?=;)|(?<=[]!\\"\')?`}\\\\w])(?=\\\\s*#|\\\\s*$)","name":"meta.function.method.with-arguments.ruby","patterns":[{"begin":"(?=[\\\\&*A-Z_a-z])","end":"(?=[,;]|\\\\s*#|\\\\s*$)","patterns":[{"include":"#method_parameters"}]},{"include":"#method_parameters"}]},{"captures":{"1":{"name":"keyword.control.def.ruby"},"3":{"name":"entity.name.function.ruby"}},"match":"(?=def\\\\b)(?<=^|\\\\s)(def)\\\\b(\\\\s+((?>[A-Z_a-z]\\\\w*(?>\\\\.|::))?(?>[A-Z_a-z]\\\\w*(?>[!?]|=(?!>))?|===?|!=|>[=>]?|<=>|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?)))?","name":"meta.function.method.without-arguments.ruby"},{"match":"\\\\b(\\\\d(?>_?\\\\d)*(\\\\.(?![^\\\\s\\\\d])(?>_?\\\\d)*)?([Ee][-+]?\\\\d(?>_?\\\\d)*)?|0(?:[Xx]\\\\h(?>_?\\\\h)*|[Oo]?[0-7](?>_?[0-7])*|[Bb][01](?>_?[01])*|[Dd]\\\\d(?>_?\\\\d)*))\\\\b","name":"constant.numeric.ruby"},{"begin":":\'","beginCaptures":{"0":{"name":"punctuation.definition.symbol.begin.ruby"}},"end":"\'","endCaptures":{"0":{"name":"punctuation.definition.symbol.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[\'\\\\\\\\]","name":"constant.character.escape.ruby"}]},{"begin":":\\"","beginCaptures":{"0":{"name":"punctuation.section.symbol.begin.ruby"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.section.symbol.end.ruby"}},"name":"constant.language.symbol.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"match":"(?<!\\\\()/=","name":"keyword.operator.assignment.augmented.ruby"},{"begin":"\'","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"\'","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.single.ruby","patterns":[{"match":"\\\\\\\\[\'\\\\\\\\]","name":"constant.character.escape.ruby"}]},{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"\\"","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.double.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"(?<!\\\\.)`","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"`","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"(?<![)\\\\w])((/))(?![*+?])(?=(?:\\\\\\\\/|[^/])*+/[eimnosux]*\\\\s*(?:[]#),.:?}]|\\\\|\\\\||&&|<=>|=>|==|=~|!~|!=|;|$|if|else|elsif|then|do|end|unless|while|until|or|and)|$)","captures":{"1":{"name":"string.regexp.interpolated.ruby"},"2":{"name":"punctuation.section.regexp.ruby"}},"contentName":"string.regexp.interpolated.ruby","end":"((/[eimnosux]*))","patterns":[{"include":"#regex_sub"}]},{"begin":"%r\\\\{","beginCaptures":{"0":{"name":"punctuation.section.regexp.begin.ruby"}},"end":"}[eimnosux]*","endCaptures":{"0":{"name":"punctuation.section.regexp.end.ruby"}},"name":"string.regexp.interpolated.ruby","patterns":[{"include":"#regex_sub"},{"include":"#nest_curly_r"}]},{"begin":"%r\\\\[","beginCaptures":{"0":{"name":"punctuation.section.regexp.begin.ruby"}},"end":"][eimnosux]*","endCaptures":{"0":{"name":"punctuation.section.regexp.end.ruby"}},"name":"string.regexp.interpolated.ruby","patterns":[{"include":"#regex_sub"},{"include":"#nest_brackets_r"}]},{"begin":"%r\\\\(","beginCaptures":{"0":{"name":"punctuation.section.regexp.begin.ruby"}},"end":"\\\\)[eimnosux]*","endCaptures":{"0":{"name":"punctuation.section.regexp.end.ruby"}},"name":"string.regexp.interpolated.ruby","patterns":[{"include":"#regex_sub"},{"include":"#nest_parens_r"}]},{"begin":"%r<","beginCaptures":{"0":{"name":"punctuation.section.regexp.begin.ruby"}},"end":">[eimnosux]*","endCaptures":{"0":{"name":"punctuation.section.regexp.end.ruby"}},"name":"string.regexp.interpolated.ruby","patterns":[{"include":"#regex_sub"},{"include":"#nest_ltgt_r"}]},{"begin":"%r(\\\\W)","beginCaptures":{"0":{"name":"punctuation.section.regexp.begin.ruby"}},"end":"\\\\1[eimnosux]*","endCaptures":{"0":{"name":"punctuation.section.regexp.end.ruby"}},"name":"string.regexp.interpolated.ruby","patterns":[{"include":"#regex_sub"}]},{"begin":"%I\\\\[","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_brackets_i"}]},{"begin":"%I\\\\(","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_parens_i"}]},{"begin":"%I<","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":">","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_ltgt_i"}]},{"begin":"%I\\\\{","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_curly_i"}]},{"begin":"%I(\\\\W)","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"%i\\\\[","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[]\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_brackets"}]},{"begin":"%i\\\\(","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[)\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_parens"}]},{"begin":"%i<","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":">","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[>\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_ltgt"}]},{"begin":"%i\\\\{","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[\\\\\\\\}]","name":"constant.character.escape.ruby"},{"include":"#nest_curly"}]},{"begin":"%i(\\\\W)","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\."}]},{"begin":"%W\\\\[","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_brackets_i"}]},{"begin":"%W\\\\(","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_parens_i"}]},{"begin":"%W<","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":">","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_ltgt_i"}]},{"begin":"%W\\\\{","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_curly_i"}]},{"begin":"%W(\\\\W)","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"%w\\\\[","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[]\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_brackets"}]},{"begin":"%w\\\\(","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[)\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_parens"}]},{"begin":"%w<","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":">","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[>\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_ltgt"}]},{"begin":"%w\\\\{","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[\\\\\\\\}]","name":"constant.character.escape.ruby"},{"include":"#nest_curly"}]},{"begin":"%w(\\\\W)","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\."}]},{"begin":"%[Qx]?\\\\(","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_parens_i"}]},{"begin":"%[Qx]?\\\\[","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_brackets_i"}]},{"begin":"%[Qx]?\\\\{","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_curly_i"}]},{"begin":"%[Qx]?<","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":">","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_ltgt_i"}]},{"begin":"%[Qx](\\\\W)","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"%([^=\\\\w\\\\s])","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.interpolated.ruby","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"%q\\\\(","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[)\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_parens"}]},{"begin":"%q<","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":">","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[>\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_ltgt"}]},{"begin":"%q\\\\[","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[]\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_brackets"}]},{"begin":"%q\\\\{","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\[\\\\\\\\}]","name":"constant.character.escape.ruby"},{"include":"#nest_curly"}]},{"begin":"%q(\\\\W)","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.definition.string.end.ruby"}},"name":"string.quoted.other.ruby","patterns":[{"match":"\\\\\\\\."}]},{"begin":"%s\\\\(","beginCaptures":{"0":{"name":"punctuation.definition.symbol.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.definition.symbol.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[)\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_parens"}]},{"begin":"%s<","beginCaptures":{"0":{"name":"punctuation.definition.symbol.begin.ruby"}},"end":">","endCaptures":{"0":{"name":"punctuation.definition.symbol.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[>\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_ltgt"}]},{"begin":"%s\\\\[","beginCaptures":{"0":{"name":"punctuation.definition.symbol.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.definition.symbol.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[]\\\\\\\\]","name":"constant.character.escape.ruby"},{"include":"#nest_brackets"}]},{"begin":"%s\\\\{","beginCaptures":{"0":{"name":"punctuation.definition.symbol.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.definition.symbol.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\[\\\\\\\\}]","name":"constant.character.escape.ruby"},{"include":"#nest_curly"}]},{"begin":"%s(\\\\W)","beginCaptures":{"0":{"name":"punctuation.definition.symbol.begin.ruby"}},"end":"\\\\1","endCaptures":{"0":{"name":"punctuation.definition.symbol.end.ruby"}},"name":"constant.language.symbol.ruby","patterns":[{"match":"\\\\\\\\."}]},{"captures":{"1":{"name":"punctuation.definition.constant.ruby"}},"match":"(?<!:)(:)(?>[$A-Z_a-z]\\\\w*(?>[!?]|=(?![=>]))?|===?|<=>|>[=>]?|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?|@@?[A-Z_a-z]\\\\w*)","name":"constant.language.symbol.ruby"},{"begin":"^=begin","captures":{"0":{"name":"punctuation.definition.comment.ruby"}},"end":"^=end","name":"comment.block.documentation.ruby"},{"include":"#yard"},{"begin":"(^[\\\\t ]+)?(?=#)","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.ruby"}},"end":"(?!\\\\G)","patterns":[{"begin":"#","beginCaptures":{"0":{"name":"punctuation.definition.comment.ruby"}},"end":"\\\\n","name":"comment.line.number-sign.ruby"}]},{"match":"(?<!\\\\w)\\\\?(\\\\\\\\(x\\\\h{1,2}(?!\\\\h)\\\\b|0[0-7]{0,2}(?![0-7])\\\\b|[^0CMx])|(\\\\\\\\[CM]-)+\\\\w|[^\\\\\\\\\\\\s])","name":"constant.numeric.ruby"},{"begin":"^__END__\\\\n","captures":{"0":{"name":"string.unquoted.program-block.ruby"}},"contentName":"text.plain","end":"(?=not)impossible","patterns":[{"begin":"(?=<?xml|<(?i:html\\\\b)|!DOCTYPE (?i:html\\\\b))","end":"(?=not)impossible","name":"text.html.embedded.ruby","patterns":[{"include":"text.html.basic"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)HTML)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.html","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)HTML)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"text.html","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"text.html.basic"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)HAML)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.haml","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)HAML)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"text.haml","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"text.haml"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)XML)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.xml","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)XML)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"text.xml","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"text.xml"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)SQL)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.sql","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)SQL)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.sql","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.sql"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)G(?:RAPHQL|QL))\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.graphql","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)G(?:RAPHQL|QL))\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.graphql","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.graphql"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)CSS)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.css","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)CSS)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.css","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.css"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)CPP)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.cpp","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)CPP)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.cpp","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.cpp"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)C)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.c","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)C)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.c","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.c"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)J(?:S|AVASCRIPT))\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.js","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)J(?:S|AVASCRIPT))\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.js","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.js"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)JQUERY)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.js.jquery","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)JQUERY)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.js.jquery","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.js.jquery"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)SH(?:|ELL))\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.shell","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)SH(?:|ELL))\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.shell","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.shell"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)LUA)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.lua","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)LUA)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.lua","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.lua"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)RUBY)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.ruby","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)RUBY)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.ruby","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.ruby"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)YA?ML)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.yaml","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)YA?ML)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"source.yaml","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"source.yaml"},{"include":"#escaped_char"}]}]},{"begin":"(?=(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)SLIM)\\\\b\\\\1))","end":"(?!\\\\G)","name":"meta.embedded.block.slim","patterns":[{"begin":"(?><<[-~]([\\"\'`]?)((?:[_\\\\w]+_|)SLIM)\\\\b\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"text.slim","end":"^\\\\s*\\\\2$\\\\n?","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"text.slim"},{"include":"#escaped_char"}]}]},{"begin":"(?>=\\\\s*<<([\\"\'`]?)(\\\\w+)\\\\1)","beginCaptures":{"0":{"name":"string.definition.begin.ruby"}},"contentName":"string.unquoted.heredoc.ruby","end":"^\\\\2$","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"(?>((<<[-~]([\\"\'`]?)(\\\\w+)\\\\3,\\\\s?)*<<[-~]([\\"\'`]?)(\\\\w+)\\\\5))(.*)","beginCaptures":{"1":{"name":"string.definition.begin.ruby"},"7":{"patterns":[{"include":"source.ruby"}]}},"contentName":"string.unquoted.heredoc.ruby","end":"^\\\\s*\\\\6$","endCaptures":{"0":{"name":"string.definition.end.ruby"}},"patterns":[{"include":"#heredoc"},{"include":"#interpolated_ruby"},{"include":"#escaped_char"}]},{"begin":"(?<=\\\\{|\\\\{\\\\s+|[^$0-:@-Z_a-z]do|^do|[^$0-:@-Z_a-z]do\\\\s+|^do\\\\s+)(\\\\|)","captures":{"1":{"name":"punctuation.separator.variable.ruby"}},"end":"(?<!\\\\|)(\\\\|)(?!\\\\|)","name":"meta.block.parameters.ruby","patterns":[{"begin":"(?![(,|\\\\s])","end":"(?=,|\\\\|\\\\s*)","patterns":[{"captures":{"1":{"name":"storage.type.variable.ruby"},"2":{"name":"variable.other.block.ruby"}},"match":"\\\\G((?:&|\\\\*\\\\*?)?)([A-Z_a-z][_\\\\w]*)"}]},{"match":",","name":"punctuation.separator.variable.ruby"}]},{"match":"=>","name":"punctuation.separator.key-value"},{"match":"->","name":"support.function.kernel.ruby"},{"match":"<<=|%=|&{1,2}=|\\\\*=|\\\\*\\\\*=|\\\\+=|-=|\\\\^=|\\\\|{1,2}=|<<","name":"keyword.operator.assignment.augmented.ruby"},{"match":"<=>|<(?![<=])|>(?![<=>])|<=|>=|===?|=~|!=|!~|(?<=[\\\\t ])\\\\?","name":"keyword.operator.comparison.ruby"},{"match":"(?<!\\\\.)\\\\b(and|not|or)\\\\b(?![!?])","name":"keyword.operator.logical.ruby"},{"match":"(?<=^|[\\\\t !])!|&&|\\\\|\\\\||\\\\^","name":"keyword.operator.logical.ruby"},{"captures":{"1":{"name":"keyword.operator.logical.ruby"}},"match":"(&\\\\.)\\\\s*(?![A-Z])"},{"match":"([%\\\\&]|\\\\*\\\\*|[-*+/])","name":"keyword.operator.arithmetic.ruby"},{"match":"=","name":"keyword.operator.assignment.ruby"},{"match":"[|~]|>>","name":"keyword.operator.other.ruby"},{"match":";","name":"punctuation.separator.statement.ruby"},{"match":",","name":"punctuation.separator.object.ruby"},{"captures":{"1":{"name":"punctuation.separator.namespace.ruby"}},"match":"(::)\\\\s*(?=[A-Z])"},{"captures":{"1":{"name":"punctuation.separator.method.ruby"}},"match":"(\\\\.|::)\\\\s*(?![A-Z])"},{"match":":","name":"punctuation.separator.other.ruby"},{"match":"\\\\{","name":"punctuation.section.scope.begin.ruby"},{"match":"}","name":"punctuation.section.scope.end.ruby"},{"match":"\\\\[","name":"punctuation.section.array.begin.ruby"},{"match":"]","name":"punctuation.section.array.end.ruby"},{"match":"[()]","name":"punctuation.section.function.ruby"},{"begin":"(?<=[^.]\\\\.|::)(?=[A-Za-z][!0-9?A-Z_a-z]*[^!0-9?A-Z_a-z])","end":"(?<=[!0-9?A-Z_a-z])(?=[^!0-9?A-Z_a-z])","name":"meta.function-call.ruby","patterns":[{"match":"([A-Za-z][!0-9?A-Z_a-z]*)(?=[^!0-9?A-Z_a-z])","name":"entity.name.function.ruby"}]},{"begin":"([A-Za-z]\\\\w*[!?]?)(\\\\()","beginCaptures":{"1":{"name":"entity.name.function.ruby"},"2":{"name":"punctuation.section.function.ruby"}},"end":"(\\\\))","endCaptures":{"1":{"name":"punctuation.section.function.ruby"}},"name":"meta.function-call.ruby","patterns":[{"include":"$self"}]}],"repository":{"escaped_char":{"match":"\\\\\\\\(?:[0-7]{1,3}|x[A-Fa-f\\\\d]{1,2}|.)","name":"constant.character.escape.ruby"},"heredoc":{"begin":"^<<[-~]?\\\\w+","end":"$","patterns":[{"include":"$self"}]},"interpolated_ruby":{"patterns":[{"begin":"#\\\\{","beginCaptures":{"0":{"name":"punctuation.section.embedded.begin.ruby"}},"contentName":"source.ruby","end":"}","endCaptures":{"0":{"name":"punctuation.section.embedded.end.ruby"}},"name":"meta.embedded.line.ruby","patterns":[{"include":"#nest_curly_and_self"},{"include":"$self"}]},{"captures":{"1":{"name":"punctuation.definition.variable.ruby"}},"match":"(#@)[A-Z_a-z]\\\\w*","name":"variable.other.readwrite.instance.ruby"},{"captures":{"1":{"name":"punctuation.definition.variable.ruby"}},"match":"(#@@)[A-Z_a-z]\\\\w*","name":"variable.other.readwrite.class.ruby"},{"captures":{"1":{"name":"punctuation.definition.variable.ruby"}},"match":"(#\\\\$)[A-Z_a-z]\\\\w*","name":"variable.other.readwrite.global.ruby"}]},"method_parameters":{"patterns":[{"include":"#parens"},{"include":"#braces"},{"include":"#brackets"},{"include":"#params"},{"include":"$self"}],"repository":{"braces":{"begin":"\\\\{","beginCaptures":{"0":{"name":"punctuation.section.scope.begin.ruby"}},"end":"}","endCaptures":{"0":{"name":"punctuation.section.scope.end.ruby"}},"patterns":[{"include":"#parens"},{"include":"#braces"},{"include":"#brackets"},{"include":"$self"}]},"brackets":{"begin":"\\\\[","beginCaptures":{"0":{"name":"punctuation.section.array.begin.ruby"}},"end":"]","endCaptures":{"0":{"name":"punctuation.section.array.end.ruby"}},"patterns":[{"include":"#parens"},{"include":"#braces"},{"include":"#brackets"},{"include":"$self"}]},"params":{"captures":{"1":{"name":"storage.type.variable.ruby"},"2":{"name":"constant.other.symbol.hashkey.parameter.function.ruby"},"3":{"name":"punctuation.definition.constant.ruby"},"4":{"name":"variable.parameter.function.ruby"}},"match":"\\\\G(&|\\\\*\\\\*?)?(?:([A-Z_a-z]\\\\w*[!?]?(:))|([A-Z_a-z]\\\\w*))"},"parens":{"begin":"\\\\(","beginCaptures":{"0":{"name":"punctuation.section.function.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.section.function.end.ruby"}},"patterns":[{"include":"#parens"},{"include":"#braces"},{"include":"#brackets"},{"include":"$self"}]}}},"nest_brackets":{"begin":"\\\\[","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"]","patterns":[{"include":"#nest_brackets"}]},"nest_brackets_i":{"begin":"\\\\[","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"]","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_brackets_i"}]},"nest_brackets_r":{"begin":"\\\\[","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"]","patterns":[{"include":"#regex_sub"},{"include":"#nest_brackets_r"}]},"nest_curly":{"begin":"\\\\{","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"}","patterns":[{"include":"#nest_curly"}]},"nest_curly_and_self":{"patterns":[{"begin":"\\\\{","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"}","patterns":[{"include":"#nest_curly_and_self"}]},{"include":"$self"}]},"nest_curly_i":{"begin":"\\\\{","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"}","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_curly_i"}]},"nest_curly_r":{"begin":"\\\\{","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"}","patterns":[{"include":"#regex_sub"},{"include":"#nest_curly_r"}]},"nest_ltgt":{"begin":"<","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":">","patterns":[{"include":"#nest_ltgt"}]},"nest_ltgt_i":{"begin":"<","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":">","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_ltgt_i"}]},"nest_ltgt_r":{"begin":"<","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":">","patterns":[{"include":"#regex_sub"},{"include":"#nest_ltgt_r"}]},"nest_parens":{"begin":"\\\\(","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"\\\\)","patterns":[{"include":"#nest_parens"}]},"nest_parens_i":{"begin":"\\\\(","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"\\\\)","patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"include":"#nest_parens_i"}]},"nest_parens_r":{"begin":"\\\\(","captures":{"0":{"name":"punctuation.section.scope.ruby"}},"end":"\\\\)","patterns":[{"include":"#regex_sub"},{"include":"#nest_parens_r"}]},"regex_sub":{"patterns":[{"include":"#interpolated_ruby"},{"include":"#escaped_char"},{"captures":{"1":{"name":"punctuation.definition.arbitrary-repetition.ruby"},"3":{"name":"punctuation.definition.arbitrary-repetition.ruby"}},"match":"(\\\\{)\\\\d+(,\\\\d+)?(})","name":"string.regexp.arbitrary-repetition.ruby"},{"begin":"\\\\[(?:\\\\^?])?","captures":{"0":{"name":"punctuation.definition.character-class.ruby"}},"end":"]","name":"string.regexp.character-class.ruby","patterns":[{"include":"#escaped_char"}]},{"begin":"\\\\(\\\\?#","beginCaptures":{"0":{"name":"punctuation.definition.comment.begin.ruby"}},"end":"\\\\)","endCaptures":{"0":{"name":"punctuation.definition.comment.end.ruby"}},"name":"comment.line.number-sign.ruby","patterns":[{"include":"#escaped_char"}]},{"begin":"\\\\(","captures":{"0":{"name":"punctuation.definition.group.ruby"}},"end":"\\\\)","name":"string.regexp.group.ruby","patterns":[{"include":"#regex_sub"}]},{"begin":"(?<=^|\\\\s)(#)\\\\s(?=[-\\\\t !,.0-9?A-Za-z[^\\\\x00-\\\\x7F]]*$)","beginCaptures":{"1":{"name":"punctuation.definition.comment.ruby"}},"end":"$\\\\n?","endCaptures":{"0":{"name":"punctuation.definition.comment.ruby"}},"name":"comment.line.number-sign.ruby"}]},"yard":{"patterns":[{"include":"#yard_comment"},{"include":"#yard_param_types"},{"include":"#yard_option"},{"include":"#yard_tag"},{"include":"#yard_types"},{"include":"#yard_directive"},{"include":"#yard_see"},{"include":"#yard_macro_attribute"}]},"yard_comment":{"begin":"^(\\\\s*)(#)(\\\\s*)(@)(abstract|api|author|deprecated|example|macro|note|overload|since|todo|version)(?=\\\\s|$)","beginCaptures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"}},"contentName":"comment.line.string.yard.ruby","end":"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)","name":"comment.line.number-sign.ruby","patterns":[{"include":"#yard"},{"include":"#yard_continuation"}]},"yard_continuation":{"match":"^\\\\s*#","name":"punctuation.definition.comment.ruby"},"yard_directive":{"begin":"^(\\\\s*)(#)(\\\\s*)(@!)(endgroup|group|method|parse|scope|visibility)(\\\\s+((\\\\[).+(])))?(?=\\\\s)","beginCaptures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"},"7":{"name":"comment.line.type.yard.ruby"},"8":{"name":"comment.line.punctuation.yard.ruby"},"9":{"name":"comment.line.punctuation.yard.ruby"}},"contentName":"comment.line.string.yard.ruby","end":"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)","name":"comment.line.number-sign.ruby","patterns":[{"include":"#yard"},{"include":"#yard_continuation"}]},"yard_macro_attribute":{"begin":"^(\\\\s*)(#)(\\\\s*)(@!)(attribute|macro)(\\\\s+((\\\\[).+(])))?(?=\\\\s)(\\\\s+([_a-z]\\\\w*:?))?","beginCaptures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"},"7":{"name":"comment.line.type.yard.ruby"},"8":{"name":"comment.line.punctuation.yard.ruby"},"9":{"name":"comment.line.punctuation.yard.ruby"},"11":{"name":"comment.line.parameter.yard.ruby"}},"contentName":"comment.line.string.yard.ruby","end":"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)","name":"comment.line.number-sign.ruby","patterns":[{"include":"#yard"},{"include":"#yard_continuation"}]},"yard_option":{"begin":"^(\\\\s*)(#)(\\\\s*)(@)(option)(?=\\\\s)(?>\\\\s+([_a-z]\\\\w*:?))?(?>\\\\s+((\\\\[).+(])))?(?>\\\\s+((\\\\S*)))?(?>\\\\s+((\\\\().+(\\\\))))?","beginCaptures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"},"6":{"name":"comment.line.parameter.yard.ruby"},"7":{"name":"comment.line.type.yard.ruby"},"8":{"name":"comment.line.punctuation.yard.ruby"},"9":{"name":"comment.line.punctuation.yard.ruby"},"10":{"name":"comment.line.keyword.yard.ruby"},"11":{"name":"comment.line.hashkey.yard.ruby"},"12":{"name":"comment.line.defaultvalue.yard.ruby"},"13":{"name":"comment.line.punctuation.yard.ruby"},"14":{"name":"comment.line.punctuation.yard.ruby"}},"contentName":"comment.line.string.yard.ruby","end":"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)","name":"comment.line.number-sign.ruby","patterns":[{"include":"#yard"},{"include":"#yard_continuation"}]},"yard_param_types":{"begin":"^(\\\\s*)(#)(\\\\s*)(@)(attr|attr_reader|attr_writer|yieldparam|param)(?=\\\\s)(?>\\\\s+(?>([_a-z]\\\\w*:?)|((\\\\[).+(]))))?(?>\\\\s+(?>((\\\\[).+(]))|([_a-z]\\\\w*:?)))?","beginCaptures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"},"6":{"name":"comment.line.parameter.yard.ruby"},"7":{"name":"comment.line.type.yard.ruby"},"8":{"name":"comment.line.punctuation.yard.ruby"},"9":{"name":"comment.line.punctuation.yard.ruby"},"10":{"name":"comment.line.type.yard.ruby"},"11":{"name":"comment.line.punctuation.yard.ruby"},"12":{"name":"comment.line.punctuation.yard.ruby"},"13":{"name":"comment.line.parameter.yard.ruby"}},"contentName":"comment.line.string.yard.ruby","end":"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)","name":"comment.line.number-sign.ruby","patterns":[{"include":"#yard"},{"include":"#yard_continuation"}]},"yard_see":{"begin":"^(\\\\s*)(#)(\\\\s*)(@)(see)(?=\\\\s)(\\\\s+(.+?))?(?=\\\\s|$)","beginCaptures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"},"7":{"name":"comment.line.parameter.yard.ruby"}},"contentName":"comment.line.string.yard.ruby","end":"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)","name":"comment.line.number-sign.ruby","patterns":[{"include":"#yard"},{"include":"#yard_continuation"}]},"yard_tag":{"captures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"}},"match":"^(\\\\s*)(#)(\\\\s*)(@)(private)$","name":"comment.line.number-sign.ruby"},"yard_types":{"begin":"^(\\\\s*)(#)(\\\\s*)(@)(raise|return|yield(?:return)?)(?=\\\\s)(\\\\s+((\\\\[).+(])))?","beginCaptures":{"2":{"name":"punctuation.definition.comment.ruby"},"4":{"name":"comment.line.keyword.punctuation.yard.ruby"},"5":{"name":"comment.line.keyword.yard.ruby"},"7":{"name":"comment.line.type.yard.ruby"},"8":{"name":"comment.line.punctuation.yard.ruby"},"9":{"name":"comment.line.punctuation.yard.ruby"}},"contentName":"comment.line.string.yard.ruby","end":"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)","name":"comment.line.number-sign.ruby","patterns":[{"include":"#yard"},{"include":"#yard_continuation"}]}},"scopeName":"source.ruby","embeddedLangs":["html","haml","xml","sql","graphql","css","cpp","c","javascript","shellscript","lua","yaml"],"aliases":["rb"]}')),L=[...e,...n,...t,...a,...r,...i,...u,...c,...s,...o,...d,...b,m];export{L as default};
