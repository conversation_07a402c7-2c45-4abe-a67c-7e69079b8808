import{d as f,i as a,ab as y,ac as O,ad as m}from"./index-8d2rRG_R.js";function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function l(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(r){m(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var H={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M2 2H7.07713C9.24103 2 11 3.75897 11 5.92277V11H5.92287C3.75893 11 2 9.24099 2 7.07705V2ZM4 4V7.07705C4 8.13647 4.86355 9 5.92287 9H9V5.92277C9 4.86357 8.13649 4 7.07713 4H4ZM16.9229 4C15.8635 4 15 4.86357 15 5.92277V9H18.0771C19.1365 9 20 8.13647 20 7.07705V4H16.9229ZM13 5.92277C13 3.75897 14.759 2 16.9229 2H22V7.07705C22 9.24099 20.2411 11 18.0771 11H13V5.92277ZM5.92287 15C4.86355 15 4 15.8635 4 16.9229V20H7.07713C8.13649 20 9 19.1364 9 18.0772V15H5.92287ZM2 16.9229C2 14.759 3.75893 13 5.92287 13H11V18.0772C11 20.241 9.24103 22 7.07713 22H2V16.9229ZM13 13H18.0771C20.2411 13 22 14.759 22 16.9229V22H16.9229C14.759 22 13 20.241 13 18.0772V13ZM15 15V18.0772C15 19.1364 15.8635 20 16.9229 20H20V16.9229C20 15.8635 19.1365 15 18.0771 15H15Z"}}]},d=f({name:"System3Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=y(r),p=a(()=>["t-icon","t-icon-system-3",o.value]),u=a(()=>l(l({},c.value),t.style)),C=a(()=>({class:p.value,style:u.value,onClick:v=>{var s;return(s=e.onClick)===null||s===void 0?void 0:s.call(e,{e:v})}}));return()=>O(H,C.value)}});export{d as default};
