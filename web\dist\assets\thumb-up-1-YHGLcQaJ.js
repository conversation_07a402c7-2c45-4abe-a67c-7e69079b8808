import{d as O,i as a,ab as m,ac as y,ad as b}from"./index-8d2rRG_R.js";function l(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,r)}return t}function s(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?l(Object(t),!0).forEach(function(r){b(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):l(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}var d={tag:"svg",attrs:{fill:"none",viewBox:"0 0 24 24",width:"1em",height:"1em"},children:[{tag:"path",attrs:{fill:"currentColor",d:"M10.5549 1.76416L12.27 2.33587C13.7809 2.83949 14.8 4.2534 14.8 5.846V8.29995H18.6688C20.5074 8.29995 21.9135 9.93884 21.6339 11.7561L20.4493 19.4561C20.2241 20.9196 18.9649 21.9999 17.4842 21.9999H3V10.0999H6.85013L10.5549 1.76416ZM11.6451 4.23575L8.14987 12.0999H5V19.9999H17.4842C17.9777 19.9999 18.3975 19.6398 18.4725 19.152L19.6572 11.452C19.7503 10.8462 19.2817 10.2999 18.6688 10.2999H12.8V5.846C12.8 5.1169 12.3351 4.4693 11.6451 4.23575Z"}}]},g=O({name:"ThumbUp1Icon",props:{size:{type:String},onClick:{type:Function}},setup(e,n){var{attrs:t}=n,r=a(()=>e.size),{className:o,style:c}=m(r),p=a(()=>["t-icon","t-icon-thumb-up-1",o.value]),u=a(()=>s(s({},c.value),t.style)),v=a(()=>({class:p.value,style:u.value,onClick:f=>{var i;return(i=e.onClick)===null||i===void 0?void 0:i.call(e,{e:f})}}));return()=>y(d,v.value)}});export{g as default};
